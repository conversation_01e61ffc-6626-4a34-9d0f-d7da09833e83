<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{1.} - %msg%n" />
		</Console>
		<File name="File" fileName="build/logs/spring-test.log">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{1.} - %msg%n" />
		</File>
	</Appenders>
	<Loggers>
		<Logger name="org.junit.platform" level="info" />
		<Logger name="org.springframework.test.context" level="warn" />
		<Logger name="org.springframework.test.context.TestContext" level="warn" />
		<Logger name="org.springframework.test.context.TestContextManager" level="warn" />
		<Logger name="org.springframework.test.context.ContextLoaderUtils" level="warn" />
		<Logger name="org.springframework.test.context.cache" level="warn" />
		<Logger name="org.springframework.test.context.junit4.rules" level="warn" />
		<Logger name="org.springframework.test.context.transaction.TransactionalTestExecutionListener" level="warn" />
		<Logger name="org.springframework.test.context.web" level="warn" />
		<!-- The following must be kept at DEBUG in order to test SPR-14363. -->
		<Logger name="org.springframework.test.util" level="debug" />
<!--
		<Logger name="org.springframework.test.context.support" level="info" />
		<Logger name="org.springframework.test.context.support.DelegatingSmartContextLoader" level="info" />
		<Logger name="org.springframework.test.context.support.AbstractGenericContextLoader" level="info" />
		<Logger name="org.springframework.test.context.support.AnnotationConfigContextLoader" level="info" />
		<Logger name="org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener" level="warn" />
		<Logger name="org.springframework.test.context.support.TestPropertySourceUtils" level="trace" />
		<Logger name="org.springframework.beans" level="warn" />
		<Logger name="org.springframework.test.web.servlet.result" level="debug" />
-->
		<Root level="error">
			<AppenderRef ref="Console" />
			<AppenderRef ref="File" />
		</Root>
	</Loggers>
</Configuration>
