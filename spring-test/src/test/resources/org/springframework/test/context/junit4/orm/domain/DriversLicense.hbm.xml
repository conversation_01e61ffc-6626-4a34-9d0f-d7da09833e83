<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD//EN"
		"https://hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping auto-import="true" default-lazy="false">

	<class name="org.springframework.test.context.junit4.orm.domain.DriversLicense" table="drivers_license">
		<id name="id" column="id">
			<generator class="identity" />
		</id>
		<property name="number" column="license_number" />
	</class>

</hibernate-mapping>
