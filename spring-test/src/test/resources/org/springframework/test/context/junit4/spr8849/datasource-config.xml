<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/jdbc https://www.springframework.org/schema/jdbc/spring-jdbc-4.2.xsd">

	<jdbc:embedded-database id="dataSource" database-name="#{T(java.util.UUID).randomUUID().toString()}">
		<jdbc:script location="classpath:/org/springframework/test/context/junit4/spr8849/spr8849-schema.sql" />
	</jdbc:embedded-database>

</beans>
