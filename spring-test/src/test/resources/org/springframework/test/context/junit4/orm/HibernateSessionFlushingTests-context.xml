<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context https://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/tx https://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/jdbc https://www.springframework.org/schema/jdbc/spring-jdbc.xsd">

	<context:component-scan base-package="org.springframework.test.context.junit4.orm"/>

	<tx:annotation-driven />

	<jdbc:embedded-database id="dataSource" type="HSQL">
		<jdbc:script location="classpath:/org/springframework/test/context/junit4/orm/db-schema.sql"/>
		<jdbc:script location="classpath:/org/springframework/test/context/junit4/orm/db-test-data.sql"/>
	</jdbc:embedded-database>

	<bean id="sessionFactory" class="org.springframework.orm.hibernate5.LocalSessionFactoryBean"
		  p:dataSource-ref="dataSource">
		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.HSQLDialect</prop>
				<prop key="hibernate.show_sql">false</prop>
			</props>
		</property>
		<property name="mappingResources">
			<list>
				<value>org/springframework/test/context/junit4/orm/domain/Person.hbm.xml</value>
				<value>org/springframework/test/context/junit4/orm/domain/DriversLicense.hbm.xml</value>
			</list>
		</property>
	</bean>

	<bean id="transactionManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager"
		  p:sessionFactory-ref="sessionFactory"/>

</beans>
