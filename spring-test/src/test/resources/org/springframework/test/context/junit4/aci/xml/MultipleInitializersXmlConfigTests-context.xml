<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

	<bean id="foo" class="java.lang.String">
		<constructor-arg value="foo" />
	</bean>

	<bean id="baz" class="java.lang.String">
		<constructor-arg value="global config" />
	</bean>

	<beans profile="dev">
		<bean id="baz" class="java.lang.String">
			<constructor-arg value="dev profile config" />
		</bean>
	</beans>

</beans>
