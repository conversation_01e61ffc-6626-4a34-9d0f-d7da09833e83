<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean id="employee1" class="org.springframework.beans.testfixture.beans.Employee">
		<property name="name" value="<PERSON>" />
		<property name="age" value="42" />
		<property name="company" value="Acme Widgets, Inc." />
	</bean>

	<bean id="employee2" class="org.springframework.beans.testfixture.beans.Employee">
		<property name="name" value="<PERSON>" />
		<property name="age" value="38" />
		<property name="company" value="Acme Widgets, Inc." />
	</bean>

	<bean id="pet" class="org.springframework.beans.testfixture.beans.Pet">
		<constructor-arg value="Fido" />
	</bean>

</beans>
