<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xsi:schemaLocation="http://www.springframework.org/schema/jdbc https://www.springframework.org/schema/jdbc/spring-jdbc.xsd
		http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd">

	<import resource="transactionalTests-context.xml" />

	<jdbc:initialize-database data-source="dataSource" >
		<jdbc:script location="classpath:/org/springframework/test/jdbc/data.sql"/>
	</jdbc:initialize-database>

	<bean id="employee" class="org.springframework.beans.testfixture.beans.Employee">
		<property name="name" value="<PERSON>" />
		<property name="age" value="42" />
		<property name="company" value="Acme Widgets, Inc." />
	</bean>

	<bean id="pet" class="org.springframework.beans.testfixture.beans.Pet">
		<constructor-arg value="Fido" />
	</bean>

	<bean id="foo" class="java.lang.String">
		<constructor-arg value="Foo" />
	</bean>

	<bean id="bar" class="java.lang.String">
		<constructor-arg value="Bar" />
	</bean>

</beans>
