<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:websocket="http://www.springframework.org/schema/websocket"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/websocket https://www.springframework.org/schema/websocket/spring-websocket.xsd">

	<websocket:message-broker order="2" preserve-publish-order="true">
		<websocket:stomp-endpoint path="/foo">
			<websocket:sockjs/>
		</websocket:stomp-endpoint>
		<websocket:stomp-broker-relay prefix="/topic,/queue" relay-host="relayhost" relay-port="1234"
									  client-login="clientlogin" client-passcode="clientpass"
									  system-login="syslogin" system-passcode="syspass"
									  heartbeat-send-interval="5000" heartbeat-receive-interval="5000"
									  virtual-host="spring.io"
									  user-destination-broadcast="/topic/unresolved-user-destination"
									  user-registry-broadcast="/topic/simp-user-registry"/>
	</websocket:message-broker>

	<bean id="myHandler" class="org.springframework.web.socket.config.TestWebSocketHandler"/>

</beans>
