<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:websocket="http://www.springframework.org/schema/websocket"
	   xsi:schemaLocation="
        http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/websocket https://www.springframework.org/schema/websocket/spring-websocket.xsd">

	<websocket:handlers order="2" allowed-origins="https://mydomain1.example, https://mydomain2.example">
		<websocket:mapping path="/foo" handler="fooHandler"/>
		<websocket:mapping path="/test" handler="testHandler"/>
		<websocket:handshake-handler ref="testHandshakeHandler"/>
		<websocket:handshake-interceptors>
			<bean class="org.springframework.web.socket.config.FooTestInterceptor"/>
			<ref bean="barTestInterceptor"/>
		</websocket:handshake-interceptors>
	</websocket:handlers>

	<bean id="testHandler" class="org.springframework.web.socket.config.TestWebSocketHandler"/>
	<bean id="fooHandler" class="org.springframework.web.socket.config.FooWebSocketHandler"/>

	<bean id="barTestInterceptor" class="org.springframework.web.socket.config.BarTestInterceptor"/>
	<bean id="testHandshakeHandler" class="org.springframework.web.socket.config.TestHandshakeHandler"/>

</beans>
