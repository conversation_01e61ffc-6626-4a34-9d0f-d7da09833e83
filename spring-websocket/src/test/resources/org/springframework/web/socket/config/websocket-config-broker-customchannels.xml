<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:websocket="http://www.springframework.org/schema/websocket"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/websocket https://www.springframework.org/schema/websocket/spring-websocket.xsd">

	<websocket:message-broker application-destination-prefix="/app" user-destination-prefix="/personal" validator="myValidator">
		<websocket:stomp-endpoint path="/foo,/bar">
			<websocket:handshake-handler ref="myHandler"/>
		</websocket:stomp-endpoint>
		<websocket:simple-broker prefix="/topic"/>
		<websocket:client-inbound-channel>
			<websocket:executor core-pool-size="100" max-pool-size="200" keep-alive-seconds="600"/>
			<websocket:interceptors>
				<ref bean="myInterceptor"/>
			</websocket:interceptors>
		</websocket:client-inbound-channel>
		<websocket:client-outbound-channel>
			<websocket:executor core-pool-size="101" max-pool-size="201" keep-alive-seconds="601"/>
			<websocket:interceptors>
				<ref bean="myInterceptor"/>
				<bean class="org.springframework.web.socket.config.TestChannelInterceptor"/>
			</websocket:interceptors>
		</websocket:client-outbound-channel>
		<websocket:broker-channel>
			<websocket:executor core-pool-size="102" max-pool-size="202" keep-alive-seconds="602"/>
		</websocket:broker-channel>
	</websocket:message-broker>

	<bean id="myHandler" class="org.springframework.web.socket.config.TestHandshakeHandler"/>

	<bean id="myInterceptor" class="org.springframework.web.socket.config.TestChannelInterceptor"/>

	<bean id="myValidator" class="org.springframework.web.socket.config.TestValidator"/>

</beans>
