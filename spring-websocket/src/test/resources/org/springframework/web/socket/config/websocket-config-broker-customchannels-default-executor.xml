<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:websocket="http://www.springframework.org/schema/websocket"
	   xsi:schemaLocation="
        http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/websocket https://www.springframework.org/schema/websocket/spring-websocket.xsd">

	<websocket:message-broker>
		<websocket:stomp-endpoint path="/foo"/>
		<websocket:simple-broker prefix="/topic"/>
		<websocket:client-inbound-channel>
			<websocket:interceptors>
				<ref bean="myInterceptor"/>
			</websocket:interceptors>
		</websocket:client-inbound-channel>
		<websocket:client-outbound-channel>
			<websocket:interceptors>
				<ref bean="myInterceptor"/>
			</websocket:interceptors>
		</websocket:client-outbound-channel>
		<websocket:broker-channel>
			<websocket:interceptors>
				<ref bean="myInterceptor"/>
			</websocket:interceptors>
		</websocket:broker-channel>
	</websocket:message-broker>

	<bean id="myInterceptor" class="org.springframework.web.socket.config.TestChannelInterceptor"/>

</beans>
