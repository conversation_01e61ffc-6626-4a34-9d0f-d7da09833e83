<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:websocket="http://www.springframework.org/schema/websocket"
	   xsi:schemaLocation="
        http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/websocket https://www.springframework.org/schema/websocket/spring-websocket.xsd">

	<websocket:handlers>
		<websocket:mapping path="/foo,/bar" handler="fooHandler"/>
	</websocket:handlers>

	<websocket:handlers>
		<websocket:mapping path="/test" handler="testHandler"/>
	</websocket:handlers>

	<bean id="testHandler" class="org.springframework.web.socket.config.TestWebSocketHandler"/>
	<bean id="fooHandler" class="org.springframework.web.socket.config.FooWebSocketHandler"/>

</beans>
