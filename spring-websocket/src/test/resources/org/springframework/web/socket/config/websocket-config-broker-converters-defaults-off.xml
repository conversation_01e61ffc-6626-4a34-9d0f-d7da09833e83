<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:websocket="http://www.springframework.org/schema/websocket"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/websocket https://www.springframework.org/schema/websocket/spring-websocket.xsd">

	<websocket:message-broker>

		<websocket:stomp-endpoint path="/foo" />

		<websocket:simple-broker />

		<websocket:message-converters register-defaults="false">
			<bean class="org.springframework.messaging.converter.StringMessageConverter"/>
		</websocket:message-converters>

	</websocket:message-broker>

</beans>
