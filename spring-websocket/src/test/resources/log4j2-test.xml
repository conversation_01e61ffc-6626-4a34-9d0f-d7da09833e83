<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{1.} - %msg%n" />
		</Console>
		<File name="File" fileName="build/logs/spring-websocket.log">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{1.} - %msg%n" />
		</File>
	</Appenders>
	<Loggers>
		<Logger name="org.springframework.web" level="warn" />
		<Logger name="org.springframework.web.socket" level="warn" />
		<Logger name="org.springframework.messaging" level="warn" />
		<Root level="error">
			<AppenderRef ref="Console" />
			<AppenderRef ref="File" />
		</Root>
	</Loggers>
</Configuration>
