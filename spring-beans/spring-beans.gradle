description = "Spring Beans"

apply plugin: "groovy"
apply plugin: "kotlin"

dependencies {
	api(project(":spring-core"))
	optional("jakarta.inject:jakarta.inject-api")
	optional("org.yaml:snakeyaml")
	optional("org.codehaus.groovy:groovy-xml")
	optional("org.jetbrains.kotlin:kotlin-reflect")
	optional("org.jetbrains.kotlin:kotlin-stdlib")
	testImplementation(testFixtures(project(":spring-core")))
	testImplementation("jakarta.annotation:jakarta.annotation-api")
	testFixturesApi("org.junit.jupiter:junit-jupiter-api")
	testFixturesImplementation("org.assertj:assertj-core")
}

// This module does joint compilation for Java and Groovy code with the compileGroovy task.
sourceSets {
	main.groovy.srcDirs += "src/main/java"
	main.java.srcDirs = []
}

compileGroovy {
	options.compilerArgs += "-Werror"
}

// This module also builds Kotlin code and the compileKotlin task naturally depends on
// compileJava. We need to redefine dependencies to break task cycles.
tasks.named('compileGroovy') {
	// Groovy only needs the declared dependencies (and not the result of Java compilation)
	classpath = sourceSets.main.compileClasspath
}
tasks.named('compileKotlin') {
	// Kotlin also depends on the result of Groovy compilation
	classpath += files(sourceSets.main.groovy.classesDirectory)
}
