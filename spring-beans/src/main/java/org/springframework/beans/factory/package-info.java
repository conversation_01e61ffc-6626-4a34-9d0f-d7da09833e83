/**
 * The core package implementing Spring's lightweight Inversion of Control (IoC) container.
 *
 * <p>Provides an alternative to the Singleton and Prototype design
 * patterns, including a consistent approach to configuration management.
 * Builds on the org.springframework.beans package.
 *
 * <p>This package and related packages are discussed in Chapter 11 of
 * <a href="https://www.amazon.com/exec/obidos/tg/detail/-/0764543857/">Expert One-On-One J2EE Design and Development</a>
 * by <PERSON> (Wrox, 2002).
 */
@NonNullApi
@NonNullFields
package org.springframework.beans.factory;

import org.springframework.lang.NonNullApi;
import org.springframework.lang.NonNullFields;
