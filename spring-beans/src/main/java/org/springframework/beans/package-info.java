/**
 * This package contains interfaces and classes for manipulating Java beans.
 * It is used by most other Spring packages.
 *
 * <p>A BeanWrapper object may be used to set and get bean properties,
 * singly or in bulk.
 *
 * <p>The classes in this package are discussed in Chapter 11 of
 * <a href="https://www.amazon.com/exec/obidos/tg/detail/-/0764543857/">Expert One-On-One J2EE Design and Development</a>
 * by <PERSON> (Wrox, 2002).
 */
@NonNullApi
@NonNullFields
package org.springframework.beans;

import org.springframework.lang.NonNullApi;
import org.springframework.lang.NonNullFields;
