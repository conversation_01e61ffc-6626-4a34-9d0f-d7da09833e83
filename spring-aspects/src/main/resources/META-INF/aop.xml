<?xml version="1.0"?>

<!--
	AspectJ load-time weaving config file to install common Spring aspects.
-->
<aspectj>

	<!--
	<weaver options="-showWeaveInfo"/>
	-->

	<aspects>
		<aspect name="org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect"/>
		<aspect name="org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect"/>
		<aspect name="org.springframework.transaction.aspectj.AnnotationTransactionAspect"/>
		<aspect name="org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect"/>
		<aspect name="org.springframework.cache.aspectj.AnnotationCacheAspect"/>
		<aspect name="org.springframework.cache.aspectj.JCacheCacheAspect"/>
	</aspects>

</aspectj>
